package com.itsm.auth.domain.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.UUID;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Utilisateur {
    private UUID id;
    private String nom;
    private String prenom;
    private String email;
    private String motDePasseHashe;
    private Role role;
    private LocalDateTime dateCreation;
    private LocalDateTime dateModification;
    private boolean actif;

    public static Utilisateur creerUtilisateur(String nom, String prenom, String email, String motDePasseHashe) {
        return Utilisateur.builder()
                .id(UUID.randomUUID())
                .nom(nom)
                .prenom(prenom)
                .email(email)
                .motDePasseHashe(motDePasseHashe)
                .role(Role.UTILISATEUR) // Registration is only for UTILISATEUR role
                .dateCreation(LocalDateTime.now())
                .dateModification(LocalDateTime.now())
                .actif(true)
                .build();
    }

    public void mettreAJour() {
        this.dateModification = LocalDateTime.now();
    }
}
